import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'local_database_helper.dart';

class AuthService {
  final SupabaseClient client = Supabase.instance.client;
  final LocalDatabaseHelper _localDb = LocalDatabaseHelper();
  final Connectivity _connectivity = Connectivity();

  // Add this map as a static constant in AuthService
  static const Map<String, String> departmentAcronyms = {
    'College of Information Technology and Education': 'CITE',
    'College of Nursing and Allied Health Sciences': 'CNAHS',
    'College of Liberal Arts and General Education': 'CLAGE',
    'College of Business Administration and Accountancy': 'CBAA',
    'College of Education and Human Development': 'CEHD',
    'College of Environmental Design and Engineering': 'CEDE',
    'College of Hospitality Management and Tourism': 'CHMT',
  };

  String _getAcronym(String fullDepartment) {
    // First, check if it's already an acronym
    if (departmentAcronyms.containsValue(fullDepartment)) {
      return fullDepartment;
    }

    // Try to get acronym from the map
    return departmentAcronyms[fullDepartment] ?? fullDepartment;
  }

  // New helper method to check if departments match
  bool _departmentMatches(String? eventDepartment, String userDepartment) {
    // Return false if event department is null or empty
    if (eventDepartment == null || eventDepartment.isEmpty) {
      return false;
    }

    // Get user's department acronym
    final userDeptAcronym = _getAcronym(userDepartment);

    // If event department contains comma, split and check each
    if (eventDepartment.contains(',')) {
      final eventDepts =
          eventDepartment.split(',').map((d) => d.trim()).toList();

      for (String dept in eventDepts) {
        // Check if any of the event departments match user's department
        if (dept == userDeptAcronym ||
            dept == userDepartment ||
            _getAcronym(dept) == userDeptAcronym) {
          return true;
        }
      }
      return false;
    } else {
      // Single department check
      return eventDepartment == userDeptAcronym ||
          eventDepartment == userDepartment ||
          _getAcronym(eventDepartment) == userDeptAcronym;
    }
  }

  Future<bool> signIn(String email, String password) async {
    try {
      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      return response.session != null;
    } catch (e) {
      print('Sign-in error: $e');
      return false;
    }
  }

  Future<void> signOut() async {
    await client.auth.signOut();
  }

  bool get isAuthenticated => client.auth.currentUser != null;

  String? get userEmail => client.auth.currentUser?.email;

  Future<Map<String, dynamic>?> fetchUserDetails(String email) async {
    try {
      final response = await client
          .from('users')
          .select('name, address, department, course, yearlvl, bio')
          .eq('email', email)
          .single();

      if (response != null) {
        Map<String, dynamic> userDetails = response as Map<String, dynamic>;

        // Convert department acronym to full name if needed
        if (userDetails['department'] != null) {
          String dept = userDetails['department'];
          // Check if it's an acronym
          for (var entry in departmentAcronyms.entries) {
            if (entry.value == dept) {
              // Replace acronym with full name
              userDetails['department'] = entry.key;
              break;
            }
          }
        }

        return userDetails;
      }
      return null;
    } catch (e) {
      print('Error fetching user details: $e');
      return null;
    }
  }

  Future<void> updateBio(String email, String bio) async {
    try {
      await client.from('users').update({'bio': bio}).eq('email', email);
    } catch (e) {
      print('Error updating bio: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchEvents(
      String department, String course,
      {bool isGuest = false}) async {
    try {
      final response = await client.from('BU_Events').select(
          'name, location, date, description, course, department, forguest');

      List<Map<String, dynamic>> filteredEvents = [];

      for (var event in response) {
        // Always add events with forguest = 'y' regardless of user type
        if (event['forguest'] == 'y') {
          filteredEvents.add(event);
        }
        // For non-guest users, also add department/course specific events
        else if (!isGuest) {
          // Check department match using the new helper method
          bool departmentMatches =
              _departmentMatches(event['department'], department);

          // Check course match with null safety
          bool courseMatches = event['course'] != null &&
              event['course'].toString().contains(course);

          if (departmentMatches || courseMatches) {
            filteredEvents.add(event);
          }
        }
      }

      return filteredEvents;
    } catch (e) {
      print('Error fetching events: $e');
      return [];
    }
  }

  // Cache connectivity status to avoid repeated checks
  bool? _cachedOnlineStatus;
  DateTime? _lastConnectivityCheck;

  Future<bool> _isOnline() async {
    try {
      // Use cached result if it's recent (within last 5 seconds)
      final now = DateTime.now();
      if (_cachedOnlineStatus != null && _lastConnectivityCheck != null) {
        final difference = now.difference(_lastConnectivityCheck!).inSeconds;
        if (difference < 5) {
          return _cachedOnlineStatus!;
        }
      }

      // Check connectivity
      final connectivityResult = await _connectivity.checkConnectivity();
      final isConnected = connectivityResult != ConnectivityResult.none;

      // Cache the result
      _cachedOnlineStatus = isConnected;
      _lastConnectivityCheck = now;

      return isConnected;
    } catch (e) {
      print('Error checking connectivity: $e');
      return false;
    }
  }

  Future<void> saveTask(String userEmail, String taskText,
      {String? deadlineDate, String? notifyDate}) async {
    try {
      final now = DateTime.now().toIso8601String();
      // Create task data
      Map<String, dynamic> taskData = {
        'email': userEmail,
        'task': taskText,
        'completed': 'n',
        'deadline': deadlineDate,
        'notify': notifyDate,
        'is_synced': 0,
        'created_at': now,
      };

      // Save to local database immediately
      final taskId = await _localDb.insertTask(taskData);

      // Try to save to Supabase in the background
      _saveTaskOnline(taskData, taskId);
    } catch (e) {
      print('Error saving task: $e');
      throw e;
    }
  }

  // Background method to save to Supabase without blocking UI
  Future<void> _saveTaskOnline(
      Map<String, dynamic> taskData, int taskId) async {
    try {
      if (await _isOnline()) {
        await client.from('users_tasks').insert(taskData);
        // If successful, mark as synced
        await _localDb.markTaskAsSynced(taskId);
      }
    } catch (e) {
      print('Error syncing to Supabase: $e');
      // Error is handled silently to not block the UI
    }
  }

  Future<List<Map<String, dynamic>>> fetchUserTasks(String email) async {
    try {
      // Always fetch from local database first
      final localTasks = await _localDb.getTasks(email);
      print('Fetched ${localTasks.length} tasks from local database');

      if (await _isOnline()) {
        try {
          print('Online - fetching from Supabase');

          // First, sync any unsynced local tasks to Supabase
          await syncUnsyncedTasks();

          // Then fetch from Supabase
          final response = await client
              .from('users_tasks')
              .select('*')
              .eq('email', email)
              .order('created_at', ascending: false);

          print('Fetched ${response.length} tasks from Supabase');

          // Get unsynced tasks to preserve them
          final unsyncedTasks = await _localDb.getUnsyncedTasks();
          print('Found ${unsyncedTasks.length} unsynced tasks');

          // Create a map of unsynced tasks for quick lookup
          Map<String, Map<String, dynamic>> unsyncedTasksMap = {};
          for (var task in unsyncedTasks) {
            unsyncedTasksMap[task['task']] = task;
          }

          // Update local database with Supabase data
          await _localDb.deleteAllTasks(email);

          // First insert Supabase tasks
          for (var task in response) {
            // Check if this task has unsynced changes
            if (unsyncedTasksMap.containsKey(task['task'])) {
              // Use the unsynced version instead
              await _localDb.insertTask(unsyncedTasksMap[task['task']]!);
              print('Preserved unsynced task: ${task['task']}');
            } else {
              // Use the Supabase version
              await _localDb.insertTask({
                ...task,
                'email': email,
                'is_synced': 1,
              });
            }
          }

          // Add any unsynced tasks that don't exist in Supabase
          for (var task in unsyncedTasks) {
            final exists = response.any((t) => t['task'] == task['task']);
            if (!exists) {
              await _localDb.insertTask(task);
              print('Added unsynced task not in Supabase: ${task['task']}');
            }
          }
        } catch (e) {
          print('Error fetching from Supabase: $e');
          // Continue with local data if Supabase fetch fails
        }
      }

      // Return the latest from local DB
      return await _localDb.getTasks(email);
    } catch (e) {
      print('Error in fetchUserTasks: $e');
      return [];
    }
  }

  Future<void> updateTaskCompletion(
      String email, String task, bool isCompleted) async {
    try {
      // Update local database immediately
      final tasks = await _localDb.getTasks(email);
      final taskToUpdate = tasks.firstWhere((t) => t['task'] == task);

      // Update with new completion status and mark as unsynced
      await _localDb.updateTask({
        ...taskToUpdate,
        'completed': isCompleted ? 'y' : 'n',
        'is_synced': 0,
      });

      // If online, try to update Supabase in the background
      _updateTaskCompletionOnline(email, task, isCompleted, taskToUpdate['id']);
    } catch (e) {
      print('Error updating task completion: $e');
      throw e;
    }
  }

  // Background method to update Supabase without blocking UI
  Future<void> _updateTaskCompletionOnline(
      String email, String task, bool isCompleted, int taskId) async {
    try {
      if (await _isOnline()) {
        await client
            .from('users_tasks')
            .update({'completed': isCompleted ? 'y' : 'n'})
            .eq('email', email)
            .eq('task', task);
        await _localDb.markTaskAsSynced(taskId);
      }
    } catch (e) {
      print('Error updating task completion online: $e');
      // Error is handled silently to not block the UI
    }
  }

  Future<void> deleteTask(String email, String task) async {
    try {
      // Get the task from local database
      final tasks = await _localDb.getTasks(email);
      final taskToDelete = tasks.firstWhere((t) => t['task'] == task);

      // Delete from local database immediately
      await _localDb.deleteTask(taskToDelete['id']);

      // Track the deleted task
      await _localDb.markTaskAsDeleted(email, task);

      // If online, try to delete from Supabase in the background
      _deleteTaskOnline(email, task);
    } catch (e) {
      print('Error deleting task: $e');
      throw e;
    }
  }

  // Background method to delete from Supabase without blocking UI
  Future<void> _deleteTaskOnline(String email, String task) async {
    try {
      if (await _isOnline()) {
        await client
            .from('users_tasks')
            .delete()
            .eq('email', email)
            .eq('task', task);
        // Clear the deleted task tracking after successful sync
        await _localDb.clearDeletedTasks();
      }
    } catch (e) {
      print('Error deleting task online: $e');
      // Error is handled silently to not block the UI
    }
  }

  Future<void> updateTask(
      String userEmail, String originalTaskText, String newTaskText,
      {String? newDeadlineDate, String? newNotifyDate}) async {
    try {
      print('Updating task: $originalTaskText to $newTaskText');
      print('New deadline: $newDeadlineDate');
      print('New notify: $newNotifyDate');

      // First, get the task from local database
      final tasks = await _localDb.getTasks(userEmail);
      final taskToUpdate = tasks.firstWhere(
        (t) => t['task'] == originalTaskText,
        orElse: () => throw Exception('Task not found in local database'),
      );

      // Create updated task data
      final updatedTask = {
        ...taskToUpdate,
        'task': newTaskText,
        'deadline': newDeadlineDate,
        'notify': newNotifyDate,
        'is_synced': 0, // Mark as unsynced
      };

      // Update local database immediately
      print('Updating task in local database');
      await _localDb.updateTask(updatedTask);
      print('Task updated in local database');

      // Try to update Supabase in the background
      _updateTaskOnline(userEmail, originalTaskText, newTaskText,
          newDeadlineDate, newNotifyDate, taskToUpdate['id']);
    } catch (e) {
      print('Error updating task: $e');
      throw e;
    }
  }

  // Background method to update Supabase without blocking UI
  Future<void> _updateTaskOnline(
      String userEmail,
      String originalTaskText,
      String newTaskText,
      String? newDeadlineDate,
      String? newNotifyDate,
      int taskId) async {
    try {
      if (await _isOnline()) {
        print('Online - attempting to update task in Supabase');

        // Prepare data for Supabase update
        Map<String, dynamic> supabaseUpdates = {
          'task': newTaskText,
          'deadline': newDeadlineDate,
          'notify': newNotifyDate,
        };

        // Remove null values to avoid overwriting with nulls
        supabaseUpdates.removeWhere((key, value) => value == null);

        // Check if the task exists in Supabase
        final response = await client
            .from('users_tasks')
            .select()
            .eq('email', userEmail)
            .eq('task', originalTaskText);

        if (response.isEmpty) {
          // If task doesn't exist in Supabase, insert it
          print('Task not found in Supabase, inserting new task');
          final tasks = await _localDb.getTasks(userEmail);
          final task = tasks.firstWhere((t) => t['id'] == taskId);

          await client.from('users_tasks').insert({
            'email': userEmail,
            'task': newTaskText,
            'completed': task['completed'] ?? 'n',
            'deadline': newDeadlineDate,
            'notify': newNotifyDate,
            'created_at':
                task['created_at'] ?? DateTime.now().toIso8601String(),
          });
        } else {
          // If task exists, update it
          print('Updating existing task in Supabase');
          await client
              .from('users_tasks')
              .update(supabaseUpdates)
              .eq('email', userEmail)
              .eq('task', originalTaskText);
        }

        // Mark as synced in local database
        await _localDb.markTaskAsSynced(taskId);
        print('Task updated in Supabase and marked as synced');
      } else {
        print('Offline - task will be synced later when online');
      }
    } catch (e) {
      print('Error syncing to Supabase: $e');
      // Continue with local update even if Supabase sync fails
    }
  }

  // Add method to sync deleted tasks
  Future<void> syncDeletedTasks() async {
    if (!await _isOnline()) return;

    try {
      final deletedTasks = await _localDb.getDeletedTasks();
      print('Found ${deletedTasks.length} deleted tasks to sync');

      for (var task in deletedTasks) {
        try {
          print('Syncing deleted task: ${task['task']}');
          await client
              .from('users_tasks')
              .delete()
              .eq('email', task['email'])
              .eq('task', task['task']);

          // Mark as synced in local database
          await _localDb.markDeletedTaskAsSynced(task['id']);
          print('Deleted task synced successfully');
        } catch (e) {
          print('Error syncing deleted task: $e');
        }
      }

      // Clear the deleted tasks after successful sync
      await _localDb.clearDeletedTasks();
    } catch (e) {
      print('Error in syncDeletedTasks: $e');
    }
  }

  // Modify syncUnsyncedTasks to handle new columns
  Future<void> syncUnsyncedTasks() async {
    if (!await _isOnline()) {
      print('Cannot sync - device is offline');
      return;
    }

    try {
      print('Starting sync of unsynced tasks');

      // Get all unsynced tasks
      final unsyncedTasks = await _localDb.getUnsyncedTasks();
      print('Found ${unsyncedTasks.length} unsynced tasks to sync');

      if (unsyncedTasks.isEmpty) {
        print('No unsynced tasks to sync');
        return;
      }

      for (var task in unsyncedTasks) {
        try {
          print('Syncing task: ${task['task']}');

          // Prepare task data for Supabase
          Map<String, dynamic> taskData = {
            'email': task['email'],
            'task': task['task'],
            'completed': task['completed'],
            'deadline': task['deadline'],
            'notify': task['notify'],
            'created_at':
                task['created_at'] ?? DateTime.now().toIso8601String(),
          };

          // Check if this task exists in Supabase by email and task text
          final existingTasks = await client
              .from('users_tasks')
              .select()
              .eq('email', task['email'])
              .eq('task', task['task']);

          print('Found ${existingTasks.length} matching tasks in Supabase');

          if (existingTasks.isEmpty) {
            // If task doesn't exist, insert it
            print('Inserting new task to Supabase: ${task['task']}');
            await client.from('users_tasks').insert(taskData);
            print('Task inserted successfully');
          } else {
            // If task exists, update it
            print('Updating existing task in Supabase: ${task['task']}');
            await client
                .from('users_tasks')
                .update(taskData)
                .eq('email', task['email'])
                .eq('task', task['task']);
            print('Task updated successfully');
          }

          // Mark as synced in local database
          await _localDb.markTaskAsSynced(task['id']);
          print('Task marked as synced locally: ${task['task']}');
        } catch (e) {
          print('Error syncing task ${task['task']}: $e');
        }
      }

      print('Sync of unsynced tasks completed');
    } catch (e) {
      print('Error in syncUnsyncedTasks: $e');
    }
  }

  // Image handling methods

  Future<void> ensureUserImagesBucketExists() async {
    try {
      final buckets = await client.storage.listBuckets();
      final bucketExists = buckets.any((bucket) => bucket.name == 'userimages');

      if (!bucketExists) {
        await client.storage
            .createBucket('userimages', const BucketOptions(public: true));
      }
    } catch (e) {
      print('Error ensuring bucket exists: $e');
    }
  }

  // Method to update user profile image
  Future<String?> updateProfileImage(String email, File imageFile) async {
    try {
      await ensureUserImagesBucketExists();

      // Create a sanitized filename
      final fileName =
          '${email.replaceAll('@', '_at_').replaceAll('.', '_dot_')}.jpg';

      // Upload the file
      await client.storage.from('userimages').upload(
            fileName,
            imageFile,
            fileOptions: const FileOptions(
              cacheControl: '3600',
              upsert: true, // Overwrite if exists
            ),
          );

      // Return the public URL
      return client.storage.from('userimages').getPublicUrl(fileName);
    } catch (e) {
      print('Error updating profile image: $e');
      return null;
    }
  }

  // Method to get profile image URL
  Future<String?> getProfileImageUrl(String email) async {
    try {
      // Create a sanitized filename
      final fileName =
          '${email.replaceAll('@', '_at_').replaceAll('.', '_dot_')}.jpg';

      // Check if file exists
      final files = await client.storage.from('userimages').list();
      final fileExists = files.any((file) => file.name == fileName);

      if (fileExists) {
        return client.storage.from('userimages').getPublicUrl(fileName);
      }
      return null;
    } catch (e) {
      print('Error getting profile image URL: $e');
      return null;
    }
  }

  // Method to fetch tasks only from local database (fast)
  Future<List<Map<String, dynamic>>> fetchLocalTasks(String email) async {
    try {
      final localTasks = await _localDb.getTasks(email);
      print('Fetched ${localTasks.length} tasks from local database');
      return localTasks;
    } catch (e) {
      print('Error fetching local tasks: $e');
      return [];
    }
  }
}
