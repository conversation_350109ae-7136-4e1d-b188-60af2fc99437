import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:test_1_copy_auth_events/pages/building1_navigation.dart';
import '../services/image_cache_service.dart';
import 'building3_navigation.dart';
import 'building2_navigation.dart';

class StreetViewNavigation extends StatefulWidget {
  final bool isGuest;

  const StreetViewNavigation({
    Key? key,
    required this.isGuest,
  }) : super(key: key);

  @override
  _StreetViewNavigationState createState() => _StreetViewNavigationState();
}

class _StreetViewNavigationState extends State<StreetViewNavigation>
    with AutomaticKeepAliveClientMixin {
  bool _inSelectionScreen = true;
  String? _selectedBuilding;
  bool get _isGuest => widget.isGuest;
  final ImageCacheService _imageCacheService = ImageCacheService();

  @override
  void initState() {
    super.initState();
    _loadNavigationState();
  }

  Future<void> _loadNavigationState() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = prefs.getString('userEmail') ?? 'guest';
    final stateKey = 'nav_state_$userEmail';

    setState(() {
      _inSelectionScreen =
          prefs.getBool('${stateKey}_selection_screen') ?? true;
      _selectedBuilding = prefs.getString('${stateKey}_selected_building');
    });
  }

  Future<void> _saveNavigationState() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = prefs.getString('userEmail') ?? 'guest';
    final stateKey = 'nav_state_$userEmail';

    await prefs.setBool('${stateKey}_selection_screen', _inSelectionScreen);
    if (_selectedBuilding != null) {
      await prefs.setString(
          '${stateKey}_selected_building', _selectedBuilding!);
    } else {
      await prefs.remove('${stateKey}_selected_building');
      // Clear building-specific navigation states
      await prefs.remove('${stateKey}_building1_current_main');
      await prefs.remove('${stateKey}_building1_is_reversed');
      await prefs.remove('${stateKey}_building2_current_cite');
      await prefs.remove('${stateKey}_building2_is_reversed');
      await prefs.remove('${stateKey}_building2_current_floor');
      await prefs.remove('${stateKey}_building3_current_cite');
      await prefs.remove('${stateKey}_building3_is_reversed');
      await prefs.remove('${stateKey}_building3_current_floor');
    }
  }

  String get _mapImage {
    switch (_selectedBuilding) {
      case 'Main Campus: Building 1':
        return 'assets/NAV/MAIN.jpg';
      case 'Main Campus: Building 2':
        return 'assets/NAV/MAIN.jpg';
      case 'Annex: Building 3':
        return 'assets/NAV/ANNEX.jpg';
      default:
        return 'assets/NAV/MAP.png';
    }
  }

  void _selectBuilding(String building) {
    setState(() {
      if (_selectedBuilding == building) {
        _selectedBuilding = null;
        _saveNavigationState(); // This will clear the navigation state
      } else {
        _selectedBuilding = building;
        _saveNavigationState();

        // Preload images for the selected building
        if (building == 'Main Campus: Building 1') {
          _imageCacheService.preloadBuildingImages(context, 1);
        } else if (building == 'Main Campus: Building 2') {
          _imageCacheService.preloadBuildingImages(context, 2);
        } else if (building == 'Annex: Building 3') {
          _imageCacheService.preloadBuildingImages(context, 3);
        }
      }
    });
  }

  void _confirmSelection() {
    if (_selectedBuilding != null) {
      setState(() {
        _inSelectionScreen = false;
        _saveNavigationState();
      });
    }
  }

  void _exitNavigation() async {
    setState(() {
      _inSelectionScreen = true;
    });
    await _saveNavigationState();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    if (!_inSelectionScreen) {
      if (_selectedBuilding == 'Main Campus: Building 2') {
        return WillPopScope(
          onWillPop: () async {
            _exitNavigation();
            return false;
          },
          child: Building2Navigation(
            isGuest: _isGuest,
            onExit: _exitNavigation,
            shouldResetState: _selectedBuilding == null,
          ),
        );
      } else if (_selectedBuilding == 'Annex: Building 3') {
        return WillPopScope(
          onWillPop: () async {
            _exitNavigation();
            return false;
          },
          child: Building3Navigation(
            isGuest: _isGuest,
            onExit: _exitNavigation,
            shouldResetState: _selectedBuilding == null,
          ),
        );
      } else if (_selectedBuilding == 'Main Campus: Building 1') {
        return WillPopScope(
          onWillPop: () async {
            _exitNavigation();
            return false;
          },
          child: Building1Navigation(
            isGuest: _isGuest,
            onExit: _exitNavigation,
            shouldResetState: _selectedBuilding == null,
          ),
        );
      }
    }
    return _buildSelectionScreen();
  }

  Widget _buildSelectionScreen() {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Campus Navigation',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 30),
        ),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(20)),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.info_outline),
            onPressed: _selectedBuilding != null
                ? _showBuildingInfo
                : _showSelectBuildingError,
            tooltip: 'Building Information',
          ),
        ],
      ),
      body: Column(
        children: [
          SizedBox(height: 25),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Container(
                    height: MediaQuery.of(context).size.height * 0.275,
                    width: MediaQuery.of(context).size.height * 0.425,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage(_mapImage),
                        fit: BoxFit.fill,
                      ),
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                  SizedBox(height: 15),
                  _buildBuildingOption(
                    "Main Campus: Building 1",
                    "Administrative offices and student services",
                    Icons.business,
                    "NAV/MAIN.jpg",
                  ),
                  _buildBuildingOption(
                    "Main Campus: Building 2",
                    "Classrooms and faculty offices",
                    Icons.school,
                    "NAV/MAIN.jpg",
                  ),
                  _buildBuildingOption(
                    "Annex: Building 3",
                    "Research labs and specialized facilities",
                    Icons.science,
                    "NAV/ANNEX.jpg",
                  ),
                  SizedBox(height: 25),
                  Center(
                    child: ElevatedButton(
                      onPressed:
                          _selectedBuilding != null ? _confirmSelection : null,
                      style: ElevatedButton.styleFrom(
                        padding:
                            EdgeInsets.symmetric(horizontal: 40, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15),
                        ),
                        backgroundColor: Colors.green,
                      ),
                      child: Text(
                        'Start Tour',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBuildingOption(
    String title,
    String description,
    IconData icon,
    String imagePath,
  ) {
    final bool isSelected = _selectedBuilding == title;
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () => _selectBuilding(title),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? Colors.green : Colors.grey,
            width: isSelected ? 2.0 : 1.0,
          ),
          borderRadius: BorderRadius.circular(15),
          color:
              isSelected ? Colors.green.withOpacity(0.1) : Colors.transparent,
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 40,
              color: Colors.green,
            ),
            SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showBuildingInfo() {
    Map<String, List<String>> buildingInfo = {
      'Main Campus: Building 1': [
        'Admissions Office',
        'Finance Office',
        'Canteen',
        'Book Store',
        'Office the Dean - CNAHS',
        'Faculty Room Nursing Program',
        'Demonstration Room',
        'Registrar Office',
        'BC Alumni Office - Hallway',
        'IMCI Laboratory',
        'Community Health Nursing Laboratory',
        'Medical Surgical Laboratory',
        'Nursing Arts Laboratory 3',
        'Faculty Lounge',
        'Center for Community Services - HALINA',
        'Office of the Director Center for Student Life and Activities',
        'Institutional Planning and Development',
        'Labor Room/Nursery Room',
        'Intensive Care',
      ],
      'Main Campus: Building 2': [
        'Main Entrance',
        'CAS 101',
        'Hyrdaulics Laboratory',
        'CAS 103',
        'Office of the Dean - CEDE',
        'EE Laboratory',
        'Office of the Vice President for Academic Affairs',
        'International Student Lounge',
        'Office of the Dean School of Graduate Studies',
        'CAS 106',
        'College of Law',
        'Human Resources Department',
        'CAS 202',
        'CEDE Faculty Room',
        'Drawing Room',
        'Conference Room',
        'Psychology Laboratory',
        'Office of the Dean - CLAGE',
        'CAS 205',
        'CAS 206',
        'Chemistry Laboratory 1',
        'Chemistry Laboratory 2',
        'Center for Science Laboratories and Services',
        'Supply Room',
        'CAS 305',
        'Medtech Laboratory',
        'CAS 306',
        'CAS 307',
        'CAS 308',
        'CAS 401',
        'CAS 402',
        'CAS 403',
        'Office of the Dean - CBAA',
        'Consultation Room',
        'CAS 404',
        'CAS 405',
        'CAS 406',
      ],
      'Annex: Building 3': [
        'ITB First Floor',
        'ITB 101',
        'ITB 102',
        'ITB 103',
        'ITB 104',
        'ITB Second Floor',
        'ITB 201',
        'ITB 202',
        'ITB 203',
        'ITB 204',
        'ITB Third Floor',
        'ITB 301',
        'ITB 302',
        'ITB 303',
        'ITB 304',
        'ITB Fourth Floor',
        'ITB 401',
        'ITB 402',
        'ITB 403',
        'ITB 404',
        'Medical and Dental Clinic',
        'CITS (Computer and Information Technology Services)',
        'Mac Laboratory',
        'Cisco Laboratory',
        'Center for Career and Counseling',
        'Research Laboratory',
        'Office of the Dean - CITE',
      ],
    };

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            '$_selectedBuilding Rooms and Facilities',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          content: Container(
            width: double.maxFinite,
            height:
                MediaQuery.of(context).size.height * 0.6, // Set a fixed height
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: buildingInfo[_selectedBuilding]?.length ?? 0,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Row(
                    children: [
                      Icon(Icons.location_on, color: Colors.green, size: 25),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          buildingInfo[_selectedBuilding]![index],
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Close', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _showSelectBuildingError() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'No Building Selected',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          content: Text('Please select a building first to view information.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Close',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}
