import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:intl/intl.dart'; // For date formatting
import 'package:table_calendar/table_calendar.dart'; // For calendar view
import 'package:test_1_copy_auth_events/services/auth_service.dart';
import 'package:test_1_copy_auth_events/pages/login_page.dart';
import 'package:test_1_copy_auth_events/services/bg.dart';
import 'package:test_1_copy_auth_events/services/due.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class TasksPage extends StatefulWidget {
  final String userId;
  final VoidCallback toggleTheme;

  const TasksPage({
    Key? key,
    required this.userId,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  _TasksPageState createState() => _TasksPageState();
}

class _TasksPageState extends State<TasksPage>
    with AutomaticKeepAliveClientMixin {
  // Add this to preserve state when switching tabs
  @override
  bool get wantKeepAlive => true;

  final AuthService _authService = AuthService();
  final DueNotification _dueNotification = DueNotification();
  final TaskNotificationService _taskNotificationService =
      TaskNotificationService();
  List<Map<String, dynamic>> tasks = [];
  bool showCompletedTasks = true;
  bool showOngoingTasks = true;
  bool _isLoading = true;
  bool _dataLoaded = false;
  bool _showCalendarView = false;
  bool _isGuest = false; // Track guest status
  bool _isSelectionMode = false;
  Set<int> _selectedTaskIndices = {};
  DateTime _selectedDay = DateTime.now();
  DateTime _focusedDay = DateTime.now();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _verifyUserMatch();
      _loadTasksOnce();
      _initNotifications();
    });
    _checkGuestStatus();
  }

  Future<void> _verifyUserMatch() async {
    final prefs = await SharedPreferences.getInstance();
    final currentEmail = _authService.userEmail;
    final lastEmail = prefs.getString('last_logged_in_email');

    if (currentEmail != lastEmail) {
      // Force refresh if user doesn't match
      await refreshTasks();
      await prefs.setString('last_logged_in_email', currentEmail ?? '');
    }
  }

  Future<void> _initNotifications() async {
    await _dueNotification.initNotifications();
  }

  Future<void> _checkGuestStatus() async {
    final prefs = await SharedPreferences.getInstance();
    _isGuest = prefs.getBool('isGuest') ?? false; // Check if user is a guest
  }

  Future<void> _loadTasksOnce() async {
    if (_dataLoaded) return; // Skip if data is already loaded

    final prefs = await SharedPreferences.getInstance();
    final currentEmail = _authService.userEmail;
    final cachedTasks = prefs.getString('cached_tasks_${widget.userId}');

    // First, immediately load from cache if available
    if (cachedTasks != null) {
      setState(() {
        tasks = (json.decode(cachedTasks) as List).map((task) {
          final taskMap = Map<String, dynamic>.from(task);
          if (taskMap['deadlineDate'] != null) {
            taskMap['deadlineDate'] = DateTime.parse(taskMap['deadlineDate']);
          }
          if (taskMap['notifyDate'] != null) {
            taskMap['notifyDate'] = DateTime.parse(taskMap['notifyDate']);
          }
          return taskMap;
        }).toList();
      });
    }

    // Check if we need to fetch fresh data
    final lastEmail = prefs.getString('last_logged_in_email');
    if (lastEmail != currentEmail || !_dataLoaded) {
      // Load fresh data without showing loading indicator
      try {
        final userEmail = _authService.userEmail;
        if (userEmail != null) {
          final userTasks = await _authService.fetchUserTasks(userEmail);

          if (!mounted) return;

          final processedTasks = userTasks.map((task) {
            DateTime? deadlineDate;
            if (task['deadline'] != null &&
                task['deadline'].toString().isNotEmpty) {
              try {
                deadlineDate = DateTime.parse(task['deadline'].toString());
              } catch (e) {
                print('Error parsing deadline date: ${task['deadline']} - $e');
              }
            }

            DateTime? notifyDate;
            if (task['notify'] != null &&
                task['notify'].toString().isNotEmpty) {
              try {
                notifyDate = DateTime.parse(task['notify'].toString());
              } catch (e) {
                print('Error parsing notify date: ${task['notify']} - $e');
              }
            }

            return {
              'text': task['task'],
              'isChecked': task['completed'] == 'y',
              'deadlineDate': deadlineDate,
              'notifyDate': notifyDate,
            };
          }).toList();

          setState(() {
            tasks = processedTasks;
            _dataLoaded = true;
          });

          await prefs.setString('last_logged_in_email', currentEmail ?? '');
          await _saveTasks();

          if (mounted) {
            await _dueNotification.checkAndShowDueTodayNotifications(tasks);
          }
        }
      } catch (e) {
        print('Error loading tasks: $e');
      }
    }
  }

  Future<void> _loadTasks() async {
    setState(() => _isLoading = true);
    try {
      final userEmail = _authService.userEmail;
      if (userEmail != null) {
        // First load from local database immediately
        final localTasks = await _authService.fetchLocalTasks(userEmail);

        // Process and update UI with local tasks
        final processedLocalTasks = localTasks.map((task) {
          DateTime? deadlineDate;
          if (task['deadline'] != null &&
              task['deadline'].toString().isNotEmpty) {
            try {
              deadlineDate = DateTime.parse(task['deadline']);
            } catch (e) {
              print('Error parsing deadline: ${e.toString()}');
            }
          }

          DateTime? notifyDate;
          if (task['notify'] != null && task['notify'].toString().isNotEmpty) {
            try {
              notifyDate = DateTime.parse(task['notify']);
            } catch (e) {
              print('Error parsing notify date: ${e.toString()}');
            }
          }

          return {
            'text': task['task'],
            'isChecked': task['completed'] == 'y',
            'deadlineDate': deadlineDate,
            'notifyDate': notifyDate,
          };
        }).toList();

        setState(() {
          tasks = processedLocalTasks;
          _isLoading = false;
        });

        // Save to SharedPreferences
        await _saveTasks();

        // Then try to fetch from Supabase in the background
        _fetchFromSupabaseInBackground(userEmail);
      }
    } catch (e) {
      print('Error loading tasks: $e');
      setState(() => _isLoading = false);
    }
  }

  // Background method to fetch from Supabase without blocking UI
  Future<void> _fetchFromSupabaseInBackground(String userEmail) async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult != ConnectivityResult.none) {
        // Fetch from Supabase and update local database
        final userTasks = await _authService.fetchUserTasks(userEmail);

        // Process tasks
        final processedTasks = userTasks.map((task) {
          DateTime? deadlineDate;
          if (task['deadline'] != null &&
              task['deadline'].toString().isNotEmpty) {
            try {
              deadlineDate = DateTime.parse(task['deadline']);
            } catch (e) {
              print('Error parsing deadline: ${e.toString()}');
            }
          }

          DateTime? notifyDate;
          if (task['notify'] != null && task['notify'].toString().isNotEmpty) {
            try {
              notifyDate = DateTime.parse(task['notify']);
            } catch (e) {
              print('Error parsing notify date: ${e.toString()}');
            }
          }

          return {
            'text': task['task'],
            'isChecked': task['completed'] == 'y',
            'deadlineDate': deadlineDate,
            'notifyDate': notifyDate,
          };
        }).toList();

        if (mounted) {
          setState(() {
            tasks = processedTasks;
          });

          // Save to SharedPreferences
          await _saveTasks();

          // Check for notifications
          await _dueNotification.checkAndShowDueTodayNotifications(tasks);
        }
      }
    } catch (e) {
      print('Error in background fetch: $e');
    }
  }

  // Synchronous method to fetch from Supabase and update UI (for refresh)
  Future<void> _fetchFromSupabaseAndUpdate(String userEmail) async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult != ConnectivityResult.none) {
        // Fetch from Supabase and update local database
        final userTasks = await _authService.fetchUserTasks(userEmail);

        // Process tasks
        final processedTasks = userTasks.map((task) {
          DateTime? deadlineDate;
          if (task['deadline'] != null &&
              task['deadline'].toString().isNotEmpty) {
            try {
              deadlineDate = DateTime.parse(task['deadline']);
            } catch (e) {
              print('Error parsing deadline: ${e.toString()}');
            }
          }

          DateTime? notifyDate;
          if (task['notify'] != null && task['notify'].toString().isNotEmpty) {
            try {
              notifyDate = DateTime.parse(task['notify']);
            } catch (e) {
              print('Error parsing notify date: ${e.toString()}');
            }
          }

          return {
            'text': task['task'],
            'isChecked': task['completed'] == 'y',
            'deadlineDate': deadlineDate,
            'notifyDate': notifyDate,
          };
        }).toList();

        if (mounted) {
          setState(() {
            tasks = processedTasks;
          });

          // Save to SharedPreferences
          await _saveTasks();

          // Check for notifications
          await _dueNotification.checkAndShowDueTodayNotifications(tasks);
        }
      }
    } catch (e) {
      print('Error in Supabase fetch and update: $e');
    }
  }

  Future<void> refreshTasks() async {
    setState(() => _isLoading = true);
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('tasks_loaded_${widget.userId}', false);

      final userEmail = _authService.userEmail;
      if (userEmail != null) {
        // Load local tasks first
        final localTasks = await _authService.fetchLocalTasks(userEmail);
        final processedLocalTasks = localTasks.map((task) {
          return {
            'text': task['text'],
            'isChecked': task['isChecked'] ?? false,
            'deadlineDate': task['deadlineDate'] != null
                ? DateTime.parse(task['deadlineDate'])
                : null,
            'notifyDate': task['notifyDate'] != null
                ? DateTime.parse(task['notifyDate'])
                : null,
          };
        }).toList();

        setState(() {
          tasks = processedLocalTasks;
        });

        // Then fetch from Supabase and wait for completion
        await _fetchFromSupabaseAndUpdate(userEmail);
      }

      await prefs.setBool('tasks_loaded_${widget.userId}', true);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Tasks refreshed successfully'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('Error refreshing tasks: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to refresh tasks: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> addTask(String taskText,
      {DateTime? deadlineDate, DateTime? notifyDate}) async {
    try {
      final userEmail = _authService.userEmail;
      if (userEmail != null) {
        print(
            'Adding task: $taskText with deadline: $deadlineDate and notify: $notifyDate');

        // Convert DateTime objects to ISO8601 strings before sending to saveTask
        await _authService.saveTask(
          userEmail,
          taskText,
          deadlineDate: deadlineDate?.toIso8601String(),
          notifyDate: notifyDate?.toIso8601String(),
        );

        setState(() {
          tasks.add({
            'text': taskText,
            'isChecked': false,
            'deadlineDate': deadlineDate,
            'notifyDate': notifyDate,
          });
        });

        // Make sure to save tasks AFTER adding to the list
        await _saveTasks();

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Task added successfully',
                style: TextStyle(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white // Color for dark mode
                      : Colors.white, // Color for light mode
                ),
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      print('Detailed error saving task: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save task: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveTasks() async {
    final prefs = await SharedPreferences.getInstance();

    // Convert DateTime objects to ISO strings before encoding to JSON
    final encodableTasks = tasks.map((task) {
      final encodableTask = Map<String, dynamic>.from(task);
      if (encodableTask['deadlineDate'] != null) {
        encodableTask['deadlineDate'] =
            encodableTask['deadlineDate'].toIso8601String();
      }
      if (encodableTask['notifyDate'] != null) {
        encodableTask['notifyDate'] =
            encodableTask['notifyDate'].toIso8601String();
      }
      return encodableTask;
    }).toList();

    await prefs.setString(
        'cached_tasks_${widget.userId}', json.encode(encodableTasks));
    await prefs.setString(
        'tasks_${widget.userId}', json.encode(encodableTasks));

    // Trigger a notification check after saving tasks
    _taskNotificationService.checkForDueNotifications();
  }

  Future<void> deleteTask(int index) async {
    try {
      final task = tasks[index];
      final userEmail = _authService.userEmail;

      if (userEmail != null) {
        // Update UI immediately
        setState(() {
          tasks.removeAt(index);
        });

        // Save to local storage
        await _saveTasks();

        // Delete from database in the background
        await _authService.deleteTask(userEmail, task['text']);
      }
    } catch (e) {
      print('Error deleting task: $e');
      // Reload tasks if there was an error
      await _loadTasks();
    }
  }

  Future<void> toggleTaskCompletion(int index) async {
    try {
      final task = tasks[index];
      final userEmail = _authService.userEmail;

      if (userEmail != null) {
        // Update UI immediately
        setState(() {
          task['isChecked'] = !task['isChecked'];
        });

        // Save to local storage
        await _saveTasks();

        // Update in database in the background
        await _authService.updateTaskCompletion(
          userEmail,
          task['text'],
          task['isChecked'],
        );
      }
    } catch (e) {
      print('Error toggling task completion: $e');
      // Reload tasks if there was an error
      await _loadTasks();
    }
  }

  void showAddTaskDialog() {
    String newTask = '';
    DateTime? selectedDeadlineDateTime;
    DateTime? selectedNotifyDateTime;

    showDialog(
      context: context,
      builder: (dialogContext) {
        return StatefulBuilder(builder: (builderContext, setDialogState) {
          return AlertDialog(
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            title: Text(
              'Add New Task',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    onChanged: (value) {
                      newTask = value;
                    },
                    decoration: InputDecoration(
                      hintText: 'Enter task here',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: BorderSide(color: Colors.green),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: BorderSide(color: Colors.green, width: 2),
                      ),
                    ),
                  ),
                  SizedBox(height: 16),
                  // Deadline Date Section
                  Row(
                    children: [
                      Icon(Icons.event_available, color: Colors.red.shade700),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          selectedDeadlineDateTime == null
                              ? 'No deadline set'
                              : 'Deadline: ${DateFormat('MMM dd, yyyy - HH:mm').format(selectedDeadlineDateTime!)}',
                          style: TextStyle(
                            color: Colors.red.shade700,
                          ),
                        ),
                      ),
                      if (selectedDeadlineDateTime != null)
                        IconButton(
                          icon: Icon(Icons.clear, color: Colors.red.shade700),
                          onPressed: () {
                            setDialogState(() {
                              selectedDeadlineDateTime = null;
                            });
                          },
                          tooltip: 'Clear deadline',
                        ),
                    ],
                  ),
                  SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () async {
                      final DateTime? pickedDate = await showDatePicker(
                        context: builderContext,
                        initialDate: DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime(2100),
                        builder: (context, child) {
                          return Theme(
                            data: Theme.of(context).copyWith(
                              colorScheme: ColorScheme.light(
                                primary: Colors.red.shade700,
                                onPrimary: Colors.white,
                                onSurface: Colors.black,
                              ),
                            ),
                            child: child!,
                          );
                        },
                      );

                      if (pickedDate != null) {
                        final TimeOfDay? pickedTime = await showTimePicker(
                          context: builderContext,
                          initialTime: TimeOfDay.now(),
                          builder: (context, child) {
                            return Theme(
                              data: Theme.of(context).copyWith(
                                colorScheme: ColorScheme.light(
                                  primary: Colors.red.shade700,
                                  onPrimary: Colors.white,
                                  onSurface: Colors.black,
                                ),
                              ),
                              child: child!,
                            );
                          },
                        );

                        if (pickedTime != null) {
                          setDialogState(() {
                            selectedDeadlineDateTime = DateTime(
                              pickedDate.year,
                              pickedDate.month,
                              pickedDate.day,
                              pickedTime.hour,
                              pickedTime.minute,
                            );
                          });
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade100,
                      foregroundColor: Colors.red.shade700,
                    ),
                    child: Text('Set Deadline'),
                  ),

                  SizedBox(height: 16),
                  Divider(),
                  SizedBox(height: 16),

                  // Notification Date Section
                  Row(
                    children: [
                      Icon(Icons.notifications_active, color: Colors.blue),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          selectedNotifyDateTime == null
                              ? 'No reminder set'
                              : 'Reminder: ${DateFormat('MMM dd, yyyy - HH:mm').format(selectedNotifyDateTime!)}',
                          style: TextStyle(
                            color: Colors.blue,
                          ),
                        ),
                      ),
                      if (selectedNotifyDateTime != null)
                        IconButton(
                          icon: Icon(Icons.clear, color: Colors.blue),
                          onPressed: () {
                            setDialogState(() {
                              selectedNotifyDateTime = null;
                            });
                          },
                          tooltip: 'Clear reminder',
                        ),
                    ],
                  ),
                  SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () async {
                      final DateTime? pickedDate = await showDatePicker(
                        context: builderContext,
                        initialDate: DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime(2100),
                        builder: (context, child) {
                          return Theme(
                            data: Theme.of(context).copyWith(
                              colorScheme: ColorScheme.light(
                                primary: Colors.blue,
                                onPrimary: Colors.white,
                                onSurface: Colors.black,
                              ),
                            ),
                            child: child!,
                          );
                        },
                      );

                      if (pickedDate != null) {
                        final TimeOfDay? pickedTime = await showTimePicker(
                          context: builderContext,
                          initialTime: TimeOfDay.now(),
                          builder: (context, child) {
                            return Theme(
                              data: Theme.of(context).copyWith(
                                colorScheme: ColorScheme.light(
                                  primary: Colors.blue,
                                  onPrimary: Colors.white,
                                  onSurface: Colors.black,
                                ),
                              ),
                              child: child!,
                            );
                          },
                        );

                        if (pickedTime != null) {
                          setDialogState(() {
                            selectedNotifyDateTime = DateTime(
                              pickedDate.year,
                              pickedDate.month,
                              pickedDate.day,
                              pickedTime.hour,
                              pickedTime.minute,
                            );
                          });
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade100,
                      foregroundColor: Colors.blue,
                    ),
                    child: Text('Set Reminder'),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: Text('Cancel', style: TextStyle(color: Colors.red)),
              ),
              ElevatedButton(
                onPressed: () {
                  if (newTask.isNotEmpty) {
                    Navigator.of(dialogContext).pop();

                    // After dialog is closed, handle the task addition
                    _handleTaskAddition(newTask, selectedDeadlineDateTime,
                        selectedNotifyDateTime);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                child: Text('Save'),
              ),
            ],
          );
        });
      },
    );
  }

  Future<void> _handleTaskAddition(String taskText, DateTime? deadlineDateTime,
      DateTime? notifyDateTime) async {
    if (mounted) {
      setState(() => _isLoading = true);
    }

    try {
      await addTask(taskText,
          deadlineDate: deadlineDateTime, notifyDate: notifyDateTime);

      // Schedule notification for this new task if it has a notify date
      if (notifyDateTime != null) {
        // Force a notification check after adding a task
        _taskNotificationService.checkForDueNotifications();
      }
    } catch (e) {
      print('Error adding task: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void showTaskDetails(Map<String, dynamic> task) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: Text(
            'Task Details',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                task['text'],
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  decoration:
                      task['isChecked'] ? TextDecoration.lineThrough : null,
                ),
              ),
              SizedBox(height: 16),
              if (task['deadlineDate'] != null) ...[
                Row(
                  children: [
                    Icon(Icons.event_available, color: Colors.red.shade700),
                    SizedBox(width: 8),
                    Text(
                      'Due: ${DateFormat('MMM dd, yyyy - HH:mm').format(task['deadlineDate'])}',
                      style: TextStyle(color: Colors.red.shade700),
                    ),
                  ],
                ),
                SizedBox(height: 8),
              ],
              if (task['notifyDate'] != null) ...[
                Row(
                  children: [
                    Icon(Icons.notifications_active, color: Colors.blue),
                    SizedBox(width: 8),
                    Text(
                      'Reminder: ${DateFormat('MMM dd, yyyy - HH:mm').format(task['notifyDate'])}',
                      style: TextStyle(color: Colors.blue),
                    ),
                  ],
                ),
                SizedBox(height: 8),
              ],
              Divider(),
              Row(
                children: [
                  Icon(
                    task['isChecked']
                        ? Icons.check_circle
                        : Icons.pending_actions,
                    color: task['isChecked'] ? Colors.green : Colors.orange,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Text(
                    task['isChecked'] ? 'Completed' : 'Ongoing',
                    style: TextStyle(
                      fontSize: 14,
                      color: task['isChecked'] ? Colors.green : Colors.orange,
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                showEditTaskDialog(task);
              },
              child: Text('Edit', style: TextStyle(color: Colors.blue)),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: Text('Close'),
            ),
          ],
        );
      },
    );
  }

  void _showCalendarDialog() {
    Map<DateTime, List<Map<String, dynamic>>> eventMap = {};

    // Group tasks only by deadline dates
    for (var task in tasks) {
      // Add tasks with deadline dates only
      if (task['deadlineDate'] != null) {
        DateTime taskDate = DateTime(
          task['deadlineDate'].year,
          task['deadlineDate'].month,
          task['deadlineDate'].day,
        );

        if (eventMap[taskDate] == null) {
          eventMap[taskDate] = [];
        }
        eventMap[taskDate]!.add(task);
      }
    }

    // Debug print
    eventMap.forEach((date, events) {
      print('Date: $date has ${events.length} events');
    });

    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            padding: EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Task Calendar',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                SizedBox(height: 16),
                Container(
                  height: MediaQuery.of(context).size.height * 0.5,
                  child: StatefulBuilder(
                    builder: (context, setState) {
                      // Normalize selected day for comparison
                      DateTime normalizedSelectedDay = DateTime(
                        _selectedDay.year,
                        _selectedDay.month,
                        _selectedDay.day,
                      );

                      return Column(
                        children: [
                          TableCalendar(
                            firstDay: DateTime.utc(2020, 1, 1),
                            lastDay: DateTime.utc(2030, 12, 31),
                            focusedDay: _focusedDay,
                            selectedDayPredicate: (day) {
                              return isSameDay(_selectedDay, day);
                            },
                            onDaySelected: (selectedDay, focusedDay) {
                              setState(() {
                                _selectedDay = selectedDay;
                                _focusedDay = focusedDay;
                              });
                            },
                            calendarStyle: CalendarStyle(
                              todayDecoration: BoxDecoration(
                                color: Colors.green.withOpacity(0.5),
                                shape: BoxShape.circle,
                              ),
                              selectedDecoration: BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                              ),
                              markerDecoration: BoxDecoration(
                                color: Colors.green.shade800,
                                shape: BoxShape.circle,
                              ),
                            ),
                            headerStyle: HeaderStyle(
                              formatButtonVisible: false,
                              titleCentered: true,
                            ),
                            eventLoader: (day) {
                              // Normalize day for comparison
                              DateTime normalizedDay =
                                  DateTime(day.year, day.month, day.day);
                              return eventMap[normalizedDay] ?? [];
                            },
                          ),
                          SizedBox(height: 16),
                          Expanded(
                            child: _buildEventList(
                                eventMap[normalizedSelectedDay] ?? []),
                          ),
                        ],
                      );
                    },
                  ),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: Text('Close'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEventList(List<Map<String, dynamic>> dayEvents) {
    if (dayEvents.isEmpty) {
      return Center(
        child: Text('No tasks due on this day'),
      );
    }

    return ListView.builder(
      itemCount: dayEvents.length,
      itemBuilder: (context, index) {
        final task = dayEvents[index];
        final bool hasDeadline = task['deadlineDate'] != null;

        return ListTile(
          title: Text(
            task['text'],
            style: TextStyle(
              decoration: task['isChecked'] ? TextDecoration.lineThrough : null,
            ),
          ),
          leading: Icon(
            task['isChecked'] ? Icons.check_circle : Icons.circle_outlined,
            color: task['isChecked'] ? Colors.green : Colors.orange,
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (hasDeadline)
                Text(
                  'Due: ${DateFormat('HH:mm').format(task['deadlineDate'])}',
                  style: TextStyle(color: Colors.red.shade700),
                ),
            ],
          ),
          onTap: () => showTaskDetails(task),
        );
      },
    );
  }

  void _showGuestAlert() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            'Authentication Required',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text('You must be authenticated to use this feature.'),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: Text('Ok'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final completedTasks = tasks.where((task) => task['isChecked']).toList();
    final ongoingTasks = tasks.where((task) => !task['isChecked']).toList();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isSelectionMode
              ? '${_selectedTaskIndices.length} Selected'
              : 'My Tasks',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 30,
          ),
        ),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
        actions: [
          if (_isSelectionMode) ...[
            IconButton(
              icon: Icon(Icons.check_circle_outline),
              onPressed:
                  _selectedTaskIndices.isEmpty ? null : _completeSelectedTasks,
              tooltip: 'Mark as completed',
            ),
            IconButton(
              icon: Icon(Icons.delete_outline),
              onPressed:
                  _selectedTaskIndices.isEmpty ? null : _deleteSelectedTasks,
              tooltip: 'Delete selected',
            ),
            IconButton(
              icon: Icon(Icons.close),
              onPressed: () {
                setState(() {
                  _isSelectionMode = false;
                  _selectedTaskIndices.clear();
                });
              },
              tooltip: 'Cancel selection',
            ),
          ] else ...[
            // Add Select Multiple button to app bar
            if (tasks.isNotEmpty)
              IconButton(
                icon: Icon(Icons.check_circle_outline),
                onPressed: () {
                  setState(() {
                    _isSelectionMode = true;
                  });
                },
                tooltip: 'Select Multiple',
              ),
            IconButton(
              icon: Icon(Icons.calendar_month),
              onPressed: () {
                if (_isGuest) {
                  _showGuestAlert();
                } else {
                  _showCalendarDialog();
                }
              },
              tooltip: 'Calendar View',
            ),
            IconButton(
              icon: Icon(Icons.refresh),
              onPressed: () {
                if (_isGuest) {
                  _showGuestAlert();
                } else {
                  refreshTasks();
                }
              },
              tooltip: 'Refresh tasks',
            ),
          ],
        ],
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
              ),
            )
          : Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        isDarkMode
                            ? Colors.grey.shade900
                            : Colors.green.shade50,
                        isDarkMode ? Colors.black : Colors.white,
                      ],
                    ),
                  ),
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          SizedBox(height: 20),
                          Expanded(
                            child: ListView(
                              children: [
                                if (ongoingTasks.isNotEmpty) ...[
                                  _buildSectionHeader(
                                    'Ongoing Tasks',
                                    ongoingTasks.length,
                                    showOngoingTasks,
                                    () {
                                      setState(() {
                                        showOngoingTasks = !showOngoingTasks;
                                      });
                                    },
                                    Colors.orange,
                                  ),
                                  if (showOngoingTasks) ...[
                                    ListView.builder(
                                      shrinkWrap: true,
                                      physics: NeverScrollableScrollPhysics(),
                                      itemCount: ongoingTasks.length,
                                      itemBuilder: (context, index) {
                                        final task = ongoingTasks[index];
                                        return buildTaskCard(task, isDarkMode);
                                      },
                                    ),
                                  ],
                                  SizedBox(height: 15),
                                ],
                                if (completedTasks.isNotEmpty &&
                                    ongoingTasks.isNotEmpty)
                                  Divider(
                                    color: Colors.green.withOpacity(0.5),
                                    thickness: 1,
                                    height: 40,
                                  ),
                                if (completedTasks.isNotEmpty) ...[
                                  _buildSectionHeader(
                                    'Completed Tasks',
                                    completedTasks.length,
                                    showCompletedTasks,
                                    () {
                                      setState(() {
                                        showCompletedTasks =
                                            !showCompletedTasks;
                                      });
                                    },
                                    Colors.green,
                                  ),
                                  if (showCompletedTasks) ...[
                                    ListView.builder(
                                      shrinkWrap: true,
                                      physics: NeverScrollableScrollPhysics(),
                                      itemCount: completedTasks.length,
                                      itemBuilder: (context, index) {
                                        final task = completedTasks[index];
                                        return buildTaskCard(task, isDarkMode);
                                      },
                                    ),
                                  ],
                                ],
                                if (completedTasks.isEmpty &&
                                    ongoingTasks.isEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 40.0),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.check_circle_outline,
                                          size: 80,
                                          color: Colors.green.withOpacity(0.5),
                                        ),
                                        SizedBox(height: 20),
                                        Text(
                                          'No tasks yet',
                                          style: TextStyle(
                                            fontSize: 24,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.green,
                                          ),
                                        ),
                                        SizedBox(height: 10),
                                        Text(
                                          'You have yet to add any tasks.\nTap the button below to get started!',
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: Colors.grey,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                        SizedBox(height: 10),
                                        GestureDetector(
                                          onTap: () => _logout(
                                              context), // Call the logout method
                                          child: Text(
                                            "Logout",
                                            style: TextStyle(
                                              color: Colors.green,
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          if (_isGuest) {
            _showGuestAlert();
          } else {
            showAddTaskDialog();
          }
        },
        icon: Icon(Icons.add),
        label: Text('New Task'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Future<void> _logout(BuildContext context) async {
    // Sign out the user
    await _authService.signOut();

    // Clear guest state and any saved user credentials
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isGuest', false); // Clear guest state
    await prefs.remove('userEmail'); // Remove saved email
    await prefs.remove('password'); // Remove saved password if you have it

    // Clear all cached data
    final email = _authService.userEmail;
    final cacheKey = email ?? 'guest';
    await prefs.remove('profile_loaded_$cacheKey');
    await prefs.remove('tasks_loaded_${email ?? 'guest'}');

    // Clear other user-related data
    await prefs.remove('bio');
    await prefs.remove('avatar');

    // Navigate to LoginPage
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => LoginPage(toggleTheme: widget.toggleTheme),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.check_circle_outline,
          size: 80,
          color: Colors.green.withOpacity(0.5),
        ),
        SizedBox(height: 20),
        Text(
          'No tasks yet',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.green,
          ),
        ),
        SizedBox(height: 10),
        Text(
          'You have yet to add any tasks.\nTap the button below to get started!',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title, int count, bool isExpanded,
      VoidCallback onToggle, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(
                title.contains('Ongoing')
                    ? Icons.pending_actions
                    : Icons.task_alt,
                color: color,
              ),
              SizedBox(width: 10),
              Text(
                '$title: $count',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          IconButton(
            icon: Icon(
              isExpanded ? Icons.expand_less : Icons.expand_more,
              color: color,
            ),
            onPressed: onToggle,
          ),
        ],
      ),
    );
  }

  Widget buildTaskCard(Map<String, dynamic> task, bool isDarkMode) {
    final taskIndex = tasks.indexOf(task);
    final isSelected = _selectedTaskIndices.contains(taskIndex);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Dismissible(
        key: Key(task['text']),
        background: Container(
          decoration: BoxDecoration(
            color: Colors.red,
            borderRadius: BorderRadius.circular(15),
          ),
          alignment: Alignment.centerRight,
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: Icon(Icons.delete, color: Colors.white),
        ),
        direction: DismissDirection.endToStart,
        confirmDismiss: (direction) async {
          // Show confirmation dialog
          final shouldDelete = await showDialog<bool>(
            context: context,
            builder: (context) {
              return AlertDialog(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20)),
                title: Text(
                  'Confirm Deletion',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                content: Text('Are you sure you want to delete this task?'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: Text('Cancel', style: TextStyle(color: Colors.red)),
                  ),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: Text('Delete'),
                  ),
                ],
              );
            },
          );

          if (shouldDelete == true) {
            // If user confirmed deletion, delete the task
            final index = tasks.indexOf(task);
            await deleteTask(index);
          }

          // Always return false to prevent the default dismissible behavior
          // We'll handle the UI update in the deleteTask method
          return false;
        },
        child: GestureDetector(
          onTap: () {
            if (_isSelectionMode) {
              setState(() {
                if (isSelected) {
                  _selectedTaskIndices.remove(taskIndex);
                } else {
                  _selectedTaskIndices.add(taskIndex);
                }
              });
            } else {
              showTaskDetails(task);
            }
          },
          onLongPress: () {
            if (!_isSelectionMode) {
              setState(() {
                _isSelectionMode = true;
                _selectedTaskIndices.add(taskIndex);
              });
            }
          },
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: isSelected
                  ? Colors.green.withOpacity(0.3)
                  : (isDarkMode
                      ? (task['isChecked']
                          ? Colors.green.shade900.withOpacity(0.3)
                          : Colors.orange.shade900.withOpacity(0.3))
                      : (task['isChecked']
                          ? Colors.green.shade50
                          : Colors.orange.shade50)),
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
              border: Border.all(
                color: isSelected
                    ? Colors.green
                    : (task['isChecked']
                        ? Colors.green.withOpacity(0.3)
                        : Colors.orange.withOpacity(0.3)),
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Selection checkbox or regular checkbox
                    _isSelectionMode
                        ? Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? Colors.green
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                color: isSelected ? Colors.green : Colors.grey,
                                width: 2,
                              ),
                            ),
                            child: isSelected
                                ? Icon(Icons.check,
                                    size: 18, color: Colors.white)
                                : null,
                          )
                        : InkWell(
                            onTap: () =>
                                toggleTaskCompletion(tasks.indexOf(task)),
                            child: Container(
                              width: 24,
                              height: 24,
                              decoration: BoxDecoration(
                                color: task['isChecked']
                                    ? Colors.green
                                    : Colors.transparent,
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(
                                  color: task['isChecked']
                                      ? Colors.green
                                      : Colors.grey,
                                  width: 2,
                                ),
                              ),
                              child: task['isChecked']
                                  ? Icon(Icons.check,
                                      size: 18, color: Colors.white)
                                  : null,
                            ),
                          ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        task['text'],
                        style: TextStyle(
                          color: isDarkMode ? Colors.white : Colors.black87,
                          fontSize: 16,
                          decoration: task['isChecked']
                              ? TextDecoration.lineThrough
                              : null,
                          decorationColor: Colors.black54,
                          fontWeight: task['isChecked']
                              ? FontWeight.normal
                              : FontWeight.w500,
                        ),
                      ),
                    ),
                    // Only show edit button when not in selection mode
                    if (!_isSelectionMode)
                      IconButton(
                        icon: Icon(
                          Icons.edit,
                          color: Colors.blue,
                          size: 20,
                        ),
                        onPressed: () {
                          showEditTaskDialog(task);
                        },
                        padding: EdgeInsets.zero,
                        constraints: BoxConstraints(),
                        splashRadius: 20,
                      ),
                  ],
                ),
                // Deadline date display
                if (task['deadlineDate'] != null) ...[
                  SizedBox(height: 4),
                  Row(
                    children: [
                      SizedBox(width: 36), // Alignment with text
                      Icon(
                        Icons.event_available,
                        size: 14,
                        color: Colors.red.shade700,
                      ),
                      SizedBox(width: 4),
                      Text(
                        'Due: ${DateFormat('MMM dd, yyyy - HH:mm').format(task['deadlineDate'])}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.red.shade700,
                        ),
                      ),
                    ],
                  ),
                ],
                // Notification date display
                if (task['notifyDate'] != null) ...[
                  SizedBox(height: 4),
                  Row(
                    children: [
                      SizedBox(width: 36), // Alignment with text
                      Icon(
                        Icons.notifications_active,
                        size: 14,
                        color: Colors.blue,
                      ),
                      SizedBox(width: 4),
                      Text(
                        'Notify: ${DateFormat('MMM dd, yyyy - HH:mm').format(task['notifyDate'])}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  void showEditTaskDialog(Map<String, dynamic> task) {
    String editedTask = task['text'];
    DateTime? selectedDeadlineDateTime = task['deadlineDate'];
    DateTime? selectedNotifyDateTime = task['notifyDate'];
    final now = DateTime.now();

    showDialog(
      context: context,
      builder: (dialogContext) {
        return StatefulBuilder(builder: (builderContext, setDialogState) {
          return AlertDialog(
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            title: Text(
              'Edit Task',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: TextEditingController(text: editedTask),
                    onChanged: (value) {
                      editedTask = value;
                    },
                    decoration: InputDecoration(
                      hintText: 'Enter task here',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: BorderSide(color: Colors.green),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: BorderSide(color: Colors.green, width: 2),
                      ),
                    ),
                  ),
                  SizedBox(height: 16),
                  // Deadline Date Section
                  Row(
                    children: [
                      Icon(Icons.event_available, color: Colors.red.shade700),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          selectedDeadlineDateTime == null
                              ? 'No deadline set'
                              : 'Deadline: ${DateFormat('MMM dd, yyyy - HH:mm').format(selectedDeadlineDateTime!)}',
                          style: TextStyle(
                            color: Colors.red.shade700,
                          ),
                        ),
                      ),
                      if (selectedDeadlineDateTime != null)
                        IconButton(
                          icon: Icon(Icons.clear, color: Colors.red.shade700),
                          onPressed: () {
                            setDialogState(() {
                              selectedDeadlineDateTime = null;
                            });
                          },
                          tooltip: 'Clear deadline',
                        ),
                    ],
                  ),
                  SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () async {
                      final DateTime? pickedDate = await showDatePicker(
                        context: builderContext,
                        initialDate:
                            selectedDeadlineDateTime?.isAfter(now) == true
                                ? selectedDeadlineDateTime!
                                : now,
                        firstDate: now,
                        lastDate: DateTime(2100),
                        builder: (context, child) {
                          return Theme(
                            data: Theme.of(context).copyWith(
                              colorScheme: ColorScheme.light(
                                primary: Colors.red.shade700,
                                onPrimary: Colors.white,
                                onSurface: Colors.black,
                              ),
                            ),
                            child: child!,
                          );
                        },
                      );

                      if (pickedDate != null) {
                        final TimeOfDay? pickedTime = await showTimePicker(
                          context: builderContext,
                          initialTime: TimeOfDay.now(),
                          builder: (context, child) {
                            return Theme(
                              data: Theme.of(context).copyWith(
                                colorScheme: ColorScheme.light(
                                  primary: Colors.red.shade700,
                                  onPrimary: Colors.white,
                                  onSurface: Colors.black,
                                ),
                              ),
                              child: child!,
                            );
                          },
                        );

                        if (pickedTime != null) {
                          setDialogState(() {
                            selectedDeadlineDateTime = DateTime(
                              pickedDate.year,
                              pickedDate.month,
                              pickedDate.day,
                              pickedTime.hour,
                              pickedTime.minute,
                            );
                          });
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade100,
                      foregroundColor: Colors.red.shade700,
                    ),
                    child: Text('Set Deadline'),
                  ),

                  SizedBox(height: 16),
                  Divider(),
                  SizedBox(height: 16),

                  // Notification Date Section
                  Row(
                    children: [
                      Icon(Icons.notifications_active, color: Colors.blue),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          selectedNotifyDateTime == null
                              ? 'No reminder set'
                              : 'Reminder: ${DateFormat('MMM dd, yyyy - HH:mm').format(selectedNotifyDateTime!)}',
                          style: TextStyle(
                            color: Colors.blue,
                          ),
                        ),
                      ),
                      if (selectedNotifyDateTime != null)
                        IconButton(
                          icon: Icon(Icons.clear, color: Colors.blue),
                          onPressed: () {
                            setDialogState(() {
                              selectedNotifyDateTime = null;
                            });
                          },
                          tooltip: 'Clear reminder',
                        ),
                    ],
                  ),
                  SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () async {
                      final DateTime? pickedDate = await showDatePicker(
                        context: builderContext,
                        initialDate:
                            selectedNotifyDateTime?.isAfter(now) == true
                                ? selectedNotifyDateTime!
                                : now,
                        firstDate: now,
                        lastDate: DateTime(2100),
                        builder: (context, child) {
                          return Theme(
                            data: Theme.of(context).copyWith(
                              colorScheme: ColorScheme.light(
                                primary: Colors.blue,
                                onPrimary: Colors.white,
                                onSurface: Colors.black,
                              ),
                            ),
                            child: child!,
                          );
                        },
                      );

                      if (pickedDate != null) {
                        final TimeOfDay? pickedTime = await showTimePicker(
                          context: builderContext,
                          initialTime: TimeOfDay.now(),
                          builder: (context, child) {
                            return Theme(
                              data: Theme.of(context).copyWith(
                                colorScheme: ColorScheme.light(
                                  primary: Colors.blue,
                                  onPrimary: Colors.white,
                                  onSurface: Colors.black,
                                ),
                              ),
                              child: child!,
                            );
                          },
                        );

                        if (pickedTime != null) {
                          setDialogState(() {
                            selectedNotifyDateTime = DateTime(
                              pickedDate.year,
                              pickedDate.month,
                              pickedDate.day,
                              pickedTime.hour,
                              pickedTime.minute,
                            );
                          });
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade100,
                      foregroundColor: Colors.blue,
                    ),
                    child: Text('Set Reminder'),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: Text('Cancel', style: TextStyle(color: Colors.red)),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (editedTask.isNotEmpty) {
                    Navigator.of(dialogContext).pop();
                    await _updateTask(
                      task,
                      editedTask,
                      selectedDeadlineDateTime,
                      selectedNotifyDateTime,
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                child: Text('Save'),
              ),
            ],
          );
        });
      },
    );
  }

  Future<void> _updateTask(
    Map<String, dynamic> oldTask,
    String newTaskText,
    DateTime? newDeadlineDate,
    DateTime? newNotifyDate,
  ) async {
    try {
      final userEmail = _authService.userEmail;
      if (userEmail != null) {
        setState(() => _isLoading = true);

        // Convert DateTime objects to ISO8601 strings for storage
        final deadlineDateString = newDeadlineDate?.toIso8601String();
        final notifyDateString = newNotifyDate?.toIso8601String();

        print('Updating task: ${oldTask['text']} to $newTaskText');
        print('New deadline: $deadlineDateString');
        print('New notify: $notifyDateString');

        // Update task in local DB and Supabase if online
        await _authService.updateTask(
          userEmail,
          oldTask['text'],
          newTaskText,
          newDeadlineDate: deadlineDateString,
          newNotifyDate: notifyDateString,
        );

        // Update local state immediately for UI
        final taskIndex = tasks.indexOf(oldTask);
        if (taskIndex != -1) {
          setState(() {
            tasks[taskIndex] = {
              ...oldTask,
              'text': newTaskText,
              'deadlineDate': newDeadlineDate,
              'notifyDate': newNotifyDate,
            };
          });
        }

        // Save to local storage
        await _saveTasks();

        // Check connectivity to show appropriate message
        final connectivityResult = await Connectivity().checkConnectivity();
        final isOffline = connectivityResult == ConnectivityResult.none;

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isOffline
                  ? 'Task updated offline. Will sync when online.'
                  : 'Task updated successfully'),
              backgroundColor: isOffline ? Colors.orange : Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        // Force a notification check after updating the task
        if (newNotifyDate != null) {
          _taskNotificationService.checkForDueNotifications();
        }
      }
    } catch (e) {
      print('Error updating task: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update task: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _completeSelectedTasks() async {
    if (_selectedTaskIndices.isEmpty) return;

    // Show confirmation dialog
    final shouldComplete = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: Text(
            'Confirm Completion',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content:
              Text('Mark ${_selectedTaskIndices.length} tasks as completed?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Cancel', style: TextStyle(color: Colors.red)),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: Text('Confirm'),
            ),
          ],
        );
      },
    );

    if (shouldComplete != true) return;

    setState(() => _isLoading = true);

    try {
      // Sort indices in descending order to avoid index shifting issues
      final sortedIndices = _selectedTaskIndices.toList()
        ..sort((a, b) => a.compareTo(b));

      // Process each task
      for (final index in sortedIndices) {
        if (index < tasks.length) {
          final task = tasks[index];
          if (!task['isChecked']) {
            final userEmail = _authService.userEmail;
            if (userEmail != null) {
              await _authService.updateTaskCompletion(
                userEmail,
                task['text'],
                true,
              );
              task['isChecked'] = true;
            }
          }
        }
      }

      // Save changes
      await _saveTasks();

      // Exit selection mode
      setState(() {
        _isSelectionMode = false;
        _selectedTaskIndices.clear();
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Tasks marked as completed'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      print('Error completing tasks: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update tasks: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _deleteSelectedTasks() async {
    if (_selectedTaskIndices.isEmpty) return;

    // Show confirmation dialog
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: Text(
            'Confirm Deletion',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text(
              'Delete ${_selectedTaskIndices.length} tasks? This cannot be undone.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Cancel', style: TextStyle(color: Colors.red)),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text('Delete'),
            ),
          ],
        );
      },
    );

    if (shouldDelete != true) return;

    setState(() => _isLoading = true);

    try {
      // Sort indices in descending order to avoid index shifting issues
      final sortedIndices = _selectedTaskIndices.toList()
        ..sort((a, b) => b.compareTo(a));

      // Process each task
      for (final index in sortedIndices) {
        if (index < tasks.length) {
          await deleteTask(index);
        }
      }

      // Exit selection mode
      setState(() {
        _isSelectionMode = false;
        _selectedTaskIndices.clear();
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Tasks deleted successfully'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      print('Error deleting tasks: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete tasks: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _syncTasksWithSupabase() async {
    if (!mounted) return;

    try {
      final userEmail = _authService.userEmail;
      if (userEmail != null) {
        setState(() => _isLoading = true);

        print('Starting task sync process');

        // First sync unsynced tasks to Supabase
        await _authService.syncUnsyncedTasks();

        // Then refresh tasks to get the latest data
        await _loadTasks();

        print('Task sync completed');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Tasks synced with cloud'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      print('Error syncing tasks: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error syncing tasks'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
